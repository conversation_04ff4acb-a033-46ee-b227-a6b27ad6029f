{"name": "@supabase/functions-js", "version": "2.4.4", "description": "JS Client library to interact with Supabase Functions.", "main": "dist/main/index.js", "module": "dist/module/index.js", "types": "dist/module/index.d.ts", "sideEffects": false, "scripts": {"clean": "rimraf dist docs/v2", "format": "prettier --write \"{src,test}/**/*.ts\"", "build": "run-s clean format build:*", "build:main": "tsc -p tsconfig.json", "build:module": "tsc -p tsconfig.module.json", "docs": "typedoc src/index.ts --out docs/v2", "docs:json": "typedoc --json docs/v2/spec.json --excludeExternals src/index.ts", "test": "jest", "test:coverage": "jest --coverage"}, "repository": {"type": "git", "url": "git+https://github.com/supabase/functions-js.git"}, "keywords": ["functions", "supabase"], "author": "Supabase", "files": ["dist", "src"], "license": "MIT", "bugs": {"url": "https://github.com/supabase/functions-js/issues"}, "homepage": "https://github.com/supabase/functions-js#readme", "dependencies": {"@supabase/node-fetch": "^2.6.14"}, "devDependencies": {"@sebbo2002/semantic-release-jsr": "^1.0.0", "@types/jest": "^28.1.0", "@types/jsonwebtoken": "^8.5.8", "@types/node": "^18.7.0", "genversion": "^3.0.2", "jest": "^28.1.0", "jsonwebtoken": "^9.0.0", "nanoid": "^3.3.1", "npm-run-all": "^4.1.5", "openai": "^4.52.5", "prettier": "^2.6.0", "rimraf": "^3.0.2", "semantic-release-plugin-update-version-in-files": "^1.1.0", "testcontainers": "^8.5.1", "ts-jest": "^28.0.0", "ts-node": "^10.9.0", "ts-test-decorators": "^0.0.6", "typedoc": "^0.22.13", "typescript": "^4.6.2"}, "publishConfig": {"access": "public"}}
"use strict";
// Node.js WebSocket entry point
Object.defineProperty(exports, "__esModule", { value: true });
let WebSocketImpl;
if (typeof window === 'undefined') {
    // Node.js environment
    // eslint-disable-next-line @typescript-eslint/no-var-requires
    WebSocketImpl = require('ws');
}
else {
    // Browser environment
    WebSocketImpl = window.WebSocket;
}
exports.default = WebSocketImpl;
//# sourceMappingURL=WebSocket.js.map
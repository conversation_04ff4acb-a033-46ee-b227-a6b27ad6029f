const express = require('express');
const bcrypt = require('bcryptjs');
const supabase = require('../config/database');
const { authenticateAdmin } = require('../middleware/auth');
const router = express.Router();

// 修改管理员密码
router.put('/password', authenticateAdmin, async (req, res) => {
  try {
    const { newPassword } = req.body;
    
    // 这里简化处理，实际应该更新环境变量或数据库
    // 由于使用环境变量，这里只返回成功消息
    res.json({ success: true, message: '密码修改成功，请重新部署应用' });
  } catch (error) {
    res.status(500).json({ error: '密码修改失败' });
  }
});

// 修改前台密码
router.put('/reception/password', authenticateAdmin, async (req, res) => {
  try {
    const { newPassword } = req.body;
    
    // 这里简化处理，实际应该更新环境变量或数据库
    res.json({ success: true, message: '前台密码修改成功，请重新部署应用' });
  } catch (error) {
    res.status(500).json({ error: '密码修改失败' });
  }
});

// 获取所有诊室排队情况
router.get('/queue/overview', authenticateAdmin, async (req, res) => {
  try {
    const { data: clinics, error: clinicsError } = await supabase
      .from('clinics')
      .select('*')
      .eq('active', true);

    if (clinicsError) throw clinicsError;

    const overview = [];

    for (const clinic of clinics) {
      const { data: queueData, error: queueError } = await supabase
        .from('queue')
        .select(`
          *,
          patients (name, phone)
        `)
        .eq('clinic_id', clinic.id)
        .eq('status', 'waiting')
        .order('queue_number');

      if (queueError) throw queueError;

      // 为每个排队记录添加排队时间
      const enrichedQueue = queueData.map(queue => {
        const queueTime = Math.floor((new Date() - new Date(queue.created_at)) / (1000 * 60));
        return {
          ...queue,
          queue_time_minutes: queueTime
        };
      });

      overview.push({
        clinic,
        queueCount: queueData.length,
        queue: enrichedQueue
      });
    }

    res.json(overview);
  } catch (error) {
    res.status(500).json({ error: '获取排队情况失败' });
  }
});

module.exports = router;

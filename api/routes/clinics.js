const express = require('express');
const bcrypt = require('bcryptjs');
const QRCode = require('qrcode');
const supabase = require('../config/database');
const { authenticateAdmin, authenticateReception } = require('../middleware/auth');
const router = express.Router();

// 获取所有诊室
router.get('/', async (req, res) => {
  try {
    const { data, error } = await supabase
      .from('clinics')
      .select('id, name, description, active')
      .eq('active', true)
      .order('name');
    
    if (error) throw error;
    
    res.json(data);
  } catch (error) {
    res.status(500).json({ error: '获取诊室列表失败' });
  }
});

// 创建诊室
router.post('/', authenticateAdmin, async (req, res) => {
  try {
    const { name, description, password } = req.body;
    
    const hashedPassword = await bcrypt.hash(password, 10);
    
    const { data, error } = await supabase
      .from('clinics')
      .insert([{
        name,
        description,
        password: hashedPassword,
        active: true
      }])
      .select()
      .single();
    
    if (error) throw error;
    
    res.json({ success: true, clinic: data });
  } catch (error) {
    res.status(500).json({ error: '创建诊室失败' });
  }
});

// 更新诊室
router.put('/:id', authenticateAdmin, async (req, res) => {
  try {
    const { id } = req.params;
    const { name, description, password } = req.body;
    
    const updateData = { name, description };
    
    if (password) {
      updateData.password = await bcrypt.hash(password, 10);
    }
    
    const { data, error } = await supabase
      .from('clinics')
      .update(updateData)
      .eq('id', id)
      .select()
      .single();
    
    if (error) throw error;
    
    res.json({ success: true, clinic: data });
  } catch (error) {
    res.status(500).json({ error: '更新诊室失败' });
  }
});

// 删除诊室
router.delete('/:id', authenticateAdmin, async (req, res) => {
  try {
    const { id } = req.params;
    
    const { error } = await supabase
      .from('clinics')
      .update({ active: false })
      .eq('id', id);
    
    if (error) throw error;
    
    res.json({ success: true });
  } catch (error) {
    res.status(500).json({ error: '删除诊室失败' });
  }
});

// 生成诊室二维码
router.get('/:id/qrcode', authenticateAdmin, async (req, res) => {
  try {
    const { id } = req.params;

    const { data: clinic, error } = await supabase
      .from('clinics')
      .select('*')
      .eq('id', id)
      .single();

    if (error || !clinic) {
      return res.status(404).json({ error: '诊室不存在' });
    }

    // 使用环境变量中的域名，如果没有则使用请求的域名
    const domain = process.env.DOMAIN || `${req.protocol}://${req.get('host')}`;
    const qrUrl = `${domain}/patient.html?clinic=${id}`;
    const qrCode = await QRCode.toDataURL(qrUrl);

    res.json({
      success: true,
      qrCode,
      url: qrUrl,
      clinic: clinic
    });
  } catch (error) {
    res.status(500).json({ error: '生成二维码失败' });
  }
});

module.exports = router;

const express = require('express');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const supabase = require('../config/database');
const router = express.Router();

// 管理员登录
router.post('/admin/login', async (req, res) => {
  try {
    console.log('Admin login attempt:', req.body);
    const { password } = req.body;

    console.log('Checking password:', password, 'against:', process.env.ADMIN_PASSWORD);

    if (password === process.env.ADMIN_PASSWORD) {
      const token = jwt.sign(
        { role: 'admin', id: 'admin' },
        process.env.JWT_SECRET,
        { expiresIn: '24h' }
      );

      console.log('Login successful, token generated');

      res.json({
        success: true,
        token,
        role: 'admin'
      });
    } else {
      console.log('Password mismatch');
      res.status(401).json({ error: '密码错误' });
    }
  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({ error: '登录失败: ' + error.message });
  }
});

// 前台登录
router.post('/reception/login', async (req, res) => {
  try {
    console.log('Reception login attempt:', req.body);
    const { password } = req.body;

    if (password === process.env.RECEPTION_PASSWORD) {
      const token = jwt.sign(
        { role: 'reception', id: 'reception' },
        process.env.JWT_SECRET,
        { expiresIn: '24h' }
      );

      res.json({
        success: true,
        token,
        role: 'reception'
      });
    } else {
      res.status(401).json({ error: '密码错误' });
    }
  } catch (error) {
    console.error('Reception login error:', error);
    res.status(500).json({ error: '登录失败: ' + error.message });
  }
});

// 医助登录
router.post('/doctor/login', async (req, res) => {
  try {
    const { clinicId, password } = req.body;
    
    const { data: clinic, error } = await supabase
      .from('clinics')
      .select('*')
      .eq('id', clinicId)
      .single();
    
    if (error || !clinic) {
      return res.status(404).json({ error: '诊室不存在' });
    }
    
    const isValidPassword = await bcrypt.compare(password, clinic.password);
    if (!isValidPassword) {
      return res.status(401).json({ error: '密码错误' });
    }
    
    const token = jwt.sign(
      { role: 'doctor', id: clinic.id, clinicId: clinic.id },
      process.env.JWT_SECRET,
      { expiresIn: '24h' }
    );
    
    res.json({ 
      success: true, 
      token,
      role: 'doctor',
      clinic: clinic
    });
  } catch (error) {
    res.status(500).json({ error: '登录失败' });
  }
});

module.exports = router;

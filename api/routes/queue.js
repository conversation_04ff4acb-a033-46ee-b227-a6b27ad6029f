const express = require('express');
const supabase = require('../config/database');
const { authenticateDoctor, authenticateReception } = require('../middleware/auth');
const router = express.Router();

// 患者排队
router.post('/join', async (req, res) => {
  try {
    const { clinicId, name, phone } = req.body;
    
    // 检查诊室是否存在
    const { data: clinic, error: clinicError } = await supabase
      .from('clinics')
      .select('*')
      .eq('id', clinicId)
      .eq('active', true)
      .single();
    
    if (clinicError || !clinic) {
      return res.status(404).json({ error: '诊室不存在或已关闭' });
    }
    
    // 检查患者是否已存在
    let { data: patient, error: patientError } = await supabase
      .from('patients')
      .select('*')
      .eq('phone', phone)
      .single();
    
    if (patientError && patientError.code !== 'PGRST116') {
      throw patientError;
    }
    
    // 如果患者不存在，创建新患者
    if (!patient) {
      const { data: newPatient, error: createError } = await supabase
        .from('patients')
        .insert([{ name, phone }])
        .select()
        .single();
      
      if (createError) throw createError;
      patient = newPatient;
    }
    
    // 检查是否已在该诊室排队
    const { data: existingQueue, error: existingError } = await supabase
      .from('queue')
      .select('*')
      .eq('clinic_id', clinicId)
      .eq('patient_id', patient.id)
      .eq('status', 'waiting')
      .single();
    
    if (existingQueue) {
      return res.json({
        success: true,
        message: '您已在排队中',
        queue: existingQueue
      });
    }
    
    // 获取当前排队号
    const { data: lastQueue, error: lastQueueError } = await supabase
      .from('queue')
      .select('queue_number')
      .eq('clinic_id', clinicId)
      .order('queue_number', { ascending: false })
      .limit(1)
      .single();
    
    const nextQueueNumber = lastQueue ? lastQueue.queue_number + 1 : 1;
    
    // 加入排队
    const { data: queueData, error: queueError } = await supabase
      .from('queue')
      .insert([{
        clinic_id: clinicId,
        patient_id: patient.id,
        queue_number: nextQueueNumber,
        status: 'waiting',
        created_at: new Date().toISOString()
      }])
      .select(`
        *,
        patients (name, phone),
        clinics (name)
      `)
      .single();
    
    if (queueError) throw queueError;
    
    res.json({
      success: true,
      message: '排队成功',
      queue: queueData
    });
  } catch (error) {
    console.error('排队失败:', error);
    res.status(500).json({ error: '排队失败' });
  }
});

// 查询排队状态
router.get('/status/:phone', async (req, res) => {
  try {
    const { phone } = req.params;

    const { data: patient, error: patientError } = await supabase
      .from('patients')
      .select('*')
      .eq('phone', phone)
      .single();

    if (patientError || !patient) {
      return res.status(404).json({ error: '未找到患者信息' });
    }

    const { data: queueData, error: queueError } = await supabase
      .from('queue')
      .select(`
        *,
        clinics (name, description)
      `)
      .eq('patient_id', patient.id)
      .eq('status', 'waiting')
      .order('created_at', { ascending: false });

    if (queueError) throw queueError;

    // 为每个排队记录计算前面的人数和排队时间
    const enrichedQueues = await Promise.all(queueData.map(async (queue) => {
      // 计算前面还有多少人
      const { data: beforeQueue, error: beforeError } = await supabase
        .from('queue')
        .select('id')
        .eq('clinic_id', queue.clinic_id)
        .eq('status', 'waiting')
        .lt('queue_number', queue.queue_number);

      const peopleAhead = beforeQueue ? beforeQueue.length : 0;

      // 计算排队时间（分钟）
      const queueTime = Math.floor((new Date() - new Date(queue.created_at)) / (1000 * 60));

      return {
        ...queue,
        people_ahead: peopleAhead,
        queue_time_minutes: queueTime
      };
    }));

    res.json({
      success: true,
      patient,
      queues: enrichedQueues
    });
  } catch (error) {
    console.error('查询排队状态失败:', error);
    res.status(500).json({ error: '查询失败' });
  }
});

// 获取诊室排队情况
router.get('/clinic/:clinicId', authenticateDoctor, async (req, res) => {
  try {
    const { clinicId } = req.params;

    const { data: queueData, error } = await supabase
      .from('queue')
      .select(`
        *,
        patients (name, phone)
      `)
      .eq('clinic_id', clinicId)
      .eq('status', 'waiting')
      .order('queue_number');

    if (error) throw error;

    // 为每个排队记录添加排队时间
    const enrichedQueue = queueData.map(queue => {
      const queueTime = Math.floor((new Date() - new Date(queue.created_at)) / (1000 * 60));
      return {
        ...queue,
        queue_time_minutes: queueTime
      };
    });

    res.json({
      success: true,
      queue: enrichedQueue
    });
  } catch (error) {
    res.status(500).json({ error: '获取排队情况失败' });
  }
});

// 完成当前看诊
router.post('/complete/:queueId', authenticateDoctor, async (req, res) => {
  try {
    const { queueId } = req.params;

    const { data, error } = await supabase
      .from('queue')
      .update({
        status: 'completed',
        completed_at: new Date().toISOString()
      })
      .eq('id', queueId)
      .select()
      .single();

    if (error) throw error;

    res.json({
      success: true,
      message: '看诊完成',
      queue: data
    });
  } catch (error) {
    res.status(500).json({ error: '操作失败' });
  }
});

// 获取前台排队概览
router.get('/overview', authenticateReception, async (req, res) => {
  try {
    const { data: clinics, error: clinicsError } = await supabase
      .from('clinics')
      .select('*')
      .eq('active', true);

    if (clinicsError) throw clinicsError;

    const overview = [];

    for (const clinic of clinics) {
      const { data: queueData, error: queueError } = await supabase
        .from('queue')
        .select(`
          *,
          patients (name, phone)
        `)
        .eq('clinic_id', clinic.id)
        .eq('status', 'waiting')
        .order('queue_number');

      if (queueError) throw queueError;

      // 为每个排队记录添加排队时间
      const enrichedQueue = queueData.map(queue => {
        const queueTime = Math.floor((new Date() - new Date(queue.created_at)) / (1000 * 60));
        return {
          ...queue,
          queue_time_minutes: queueTime
        };
      });

      overview.push({
        clinic,
        queueCount: queueData.length,
        queue: enrichedQueue
      });
    }

    res.json(overview);
  } catch (error) {
    res.status(500).json({ error: '获取排队情况失败' });
  }
});

module.exports = router;

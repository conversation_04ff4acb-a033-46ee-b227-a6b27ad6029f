const jwt = require('jsonwebtoken');

const authenticateToken = (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    return res.status(401).json({ error: 'Access token required' });
  }

  jwt.verify(token, process.env.JWT_SECRET, (err, user) => {
    if (err) {
      return res.status(403).json({ error: 'Invalid token' });
    }
    req.user = user;
    next();
  });
};

const authenticateAdmin = (req, res, next) => {
  authenticateToken(req, res, () => {
    if (req.user.role !== 'admin') {
      return res.status(403).json({ error: 'Admin access required' });
    }
    next();
  });
};

const authenticateReception = (req, res, next) => {
  authenticateToken(req, res, () => {
    if (req.user.role !== 'reception' && req.user.role !== 'admin') {
      return res.status(403).json({ error: 'Reception or admin access required' });
    }
    next();
  });
};

const authenticateDoctor = (req, res, next) => {
  authenticateToken(req, res, () => {
    if (req.user.role !== 'doctor') {
      return res.status(403).json({ error: 'Doctor access required' });
    }
    next();
  });
};

module.exports = {
  authenticateToken,
  authenticateAdmin,
  authenticateReception,
  authenticateDoctor
};

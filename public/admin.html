<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理员后台 - 优眠医学中心</title>
    <link rel="stylesheet" href="styles/common.css">
    <link rel="stylesheet" href="styles/admin.css">
</head>
<body>
    <div class="container">
        <header class="header">
            <h1>管理员后台</h1>
            <div class="header-actions">
                <button class="btn btn-secondary" onclick="logout()">退出登录</button>
            </div>
        </header>

        <main class="main">
            <div class="admin-tabs">
                <button class="tab-btn active" onclick="showTab('clinics')">诊室管理</button>
                <button class="tab-btn" onclick="showTab('queue')">排队概览</button>
                <button class="tab-btn" onclick="showTab('settings')">系统设置</button>
            </div>

            <!-- 诊室管理 -->
            <div id="clinics-tab" class="tab-content active">
                <div class="section-header">
                    <h2>诊室管理</h2>
                    <button class="btn btn-primary" onclick="showAddClinicModal()">添加诊室</button>
                </div>

                <div class="clinics-grid" id="clinicsGrid">
                    <!-- 诊室卡片将在这里动态生成 -->
                </div>
            </div>

            <!-- 排队概览 -->
            <div id="queue-tab" class="tab-content">
                <div class="section-header">
                    <h2>排队概览</h2>
                    <button class="btn btn-primary" onclick="refreshQueue()">刷新</button>
                </div>

                <div id="queueOverview">
                    <!-- 排队概览将在这里显示 -->
                </div>
            </div>
        </main>
    </div>

    <!-- 添加诊室模态框 -->
    <div id="addClinicModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>添加诊室</h3>
                <span class="close" onclick="closeModal('addClinicModal')">&times;</span>
            </div>
            <form id="addClinicForm">
                <div class="form-group">
                    <label for="clinicName">诊室名称</label>
                    <input type="text" id="clinicName" required>
                </div>
                <div class="form-group">
                    <label for="clinicDescription">诊室描述</label>
                    <textarea id="clinicDescription" rows="3"></textarea>
                </div>
                <div class="form-group">
                    <label for="clinicPassword">诊室密码</label>
                    <input type="password" id="clinicPassword" required>
                </div>
                <div class="modal-actions">
                    <button type="button" class="btn btn-secondary" onclick="closeModal('addClinicModal')">取消</button>
                    <button type="submit" class="btn btn-primary">添加</button>
                </div>
            </form>
        </div>
    </div>

    <!-- 编辑诊室模态框 -->
    <div id="editClinicModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>编辑诊室</h3>
                <span class="close" onclick="closeModal('editClinicModal')">&times;</span>
            </div>
            <form id="editClinicForm">
                <input type="hidden" id="editClinicId">
                <div class="form-group">
                    <label for="editClinicName">诊室名称</label>
                    <input type="text" id="editClinicName" required>
                </div>
                <div class="form-group">
                    <label for="editClinicDescription">诊室描述</label>
                    <textarea id="editClinicDescription" rows="3"></textarea>
                </div>
                <div class="form-group">
                    <label for="editClinicPassword">新密码（留空则不修改）</label>
                    <input type="password" id="editClinicPassword">
                </div>
                <div class="modal-actions">
                    <button type="button" class="btn btn-secondary" onclick="closeModal('editClinicModal')">取消</button>
                    <button type="submit" class="btn btn-primary">保存</button>
                </div>
            </form>
        </div>
    </div>

    <!-- 二维码模态框 -->
    <div id="qrcodeModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>诊室二维码</h3>
                <span class="close" onclick="closeModal('qrcodeModal')">&times;</span>
            </div>
            <div class="qrcode-container">
                <div id="qrcodeDisplay"></div>
                <p id="qrcodeUrl"></p>
                <button class="btn btn-primary" onclick="downloadQRCode()">下载二维码</button>
            </div>
        </div>
    </div>

    <script src="scripts/common.js"></script>
    <script src="scripts/admin.js"></script>
</body>
</html>
